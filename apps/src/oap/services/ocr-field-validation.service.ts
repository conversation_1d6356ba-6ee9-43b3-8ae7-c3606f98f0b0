import { Injectable } from '@nestjs/common';
import { 
  getRequiredFields, 
  getDocumentTypeConfig,
  OcrFieldValidationResult 
} from '../config/ocr-document-config';
import { 
  detectTestTypeFromOcr,
  getEnglishTestRequiredFields 
} from '../config/english-proficiency-mappings';

export interface ValidationContext {
  documentType: string;
  testType?: string;
  requiredFieldsFromFrontend?: string[];
  additionalValidationRules?: Record<string, (value: any) => boolean>;
}

export interface DetailedValidationResult extends OcrFieldValidationResult {
  validFields: string[];
  fieldValidationDetails: Record<string, {
    present: boolean;
    valid: boolean;
    value: any;
    error?: string;
  }>;
}

@Injectable()
export class OcrFieldValidationService {
  
  /**
   * Validate OCR fields based on document type and context
   */
  validateOcrFields(
    ocrData: any,
    context: ValidationContext,
  ): DetailedValidationResult {
    const { documentType, testType, requiredFieldsFromFrontend } = context;
    
    // Get required fields based on document type and context
    let requiredFields: string[];
    
    if (documentType.toLowerCase() === 'proof english proficiency') {
      requiredFields = this.getEnglishProficiencyRequiredFields(
        ocrData,
        testType,
        requiredFieldsFromFrontend,
      );
    } else {
      requiredFields = getRequiredFields(documentType, testType, requiredFieldsFromFrontend);
    }

    const fieldValidationDetails: Record<string, any> = {};
    const missingFields: string[] = [];
    const validFields: string[] = [];

    // Validate each required field
    for (const field of requiredFields) {
      const fieldValue = this.getNestedValue(ocrData, field);
      const isPresent = this.isFieldPresent(fieldValue);
      const isValid = isPresent && this.validateFieldValue(field, fieldValue, context);

      fieldValidationDetails[field] = {
        present: isPresent,
        valid: isValid,
        value: fieldValue,
      };

      if (!isPresent) {
        missingFields.push(field);
      } else if (!isValid) {
        fieldValidationDetails[field].error = this.getValidationError(field, fieldValue);
        missingFields.push(field); // Treat invalid fields as missing
      } else {
        validFields.push(field);
      }
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      validFields,
      fieldValidationDetails,
    };
  }

  /**
   * Get required fields for English proficiency documents
   */
  private getEnglishProficiencyRequiredFields(
    ocrData: any,
    testType?: string,
    requiredFieldsFromFrontend?: string[],
  ): string[] {
    // If frontend provides required fields, use those
    if (requiredFieldsFromFrontend && requiredFieldsFromFrontend.length > 0) {
      return requiredFieldsFromFrontend;
    }

    // Try to detect test type from OCR data if not provided
    const detectedTestType = testType || detectTestTypeFromOcr(ocrData);
    
    if (detectedTestType) {
      return getEnglishTestRequiredFields(detectedTestType);
    }

    // Fallback to basic required fields
    return ['name_of_test', 'test_date', 'test_scores.overall_score'];
  }

  /**
   * Check if a field value is present and not empty
   */
  private isFieldPresent(value: any): boolean {
    if (value === null || value === undefined) {
      return false;
    }

    if (typeof value === 'string') {
      return value.trim().length > 0;
    }

    if (typeof value === 'number') {
      return !isNaN(value);
    }

    if (Array.isArray(value)) {
      return value.length > 0;
    }

    if (typeof value === 'object') {
      return Object.keys(value).length > 0;
    }

    return true;
  }

  /**
   * Validate field value based on field type and rules
   */
  private validateFieldValue(
    fieldName: string,
    fieldValue: any,
    context: ValidationContext,
  ): boolean {
    // Apply custom validation rules if provided
    if (context.additionalValidationRules?.[fieldName]) {
      return context.additionalValidationRules[fieldName](fieldValue);
    }

    // Apply built-in validation rules
    return this.applyBuiltInValidation(fieldName, fieldValue);
  }

  /**
   * Apply built-in validation rules
   */
  private applyBuiltInValidation(fieldName: string, fieldValue: any): boolean {
    const fieldNameLower = fieldName.toLowerCase();

    // Date validation
    if (fieldNameLower.includes('date')) {
      return this.validateDate(fieldValue);
    }

    // Score validation
    if (fieldNameLower.includes('score')) {
      return this.validateScore(fieldValue);
    }

    // Email validation
    if (fieldNameLower.includes('email')) {
      return this.validateEmail(fieldValue);
    }

    // URL validation
    if (fieldNameLower.includes('link') || fieldNameLower.includes('url')) {
      return this.validateUrl(fieldValue);
    }

    // Number validation for specific fields
    if (fieldNameLower.includes('number') || fieldNameLower.includes('trf')) {
      return this.validateAlphanumeric(fieldValue);
    }

    // Default validation - just check if value exists and is not empty
    return this.isFieldPresent(fieldValue);
  }

  /**
   * Validate date format
   */
  private validateDate(dateValue: any): boolean {
    if (typeof dateValue !== 'string') {
      return false;
    }

    // Support multiple date formats
    const dateFormats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
      /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
      /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
    ];

    const isValidFormat = dateFormats.some(format => format.test(dateValue));
    if (!isValidFormat) {
      return false;
    }

    // Try to parse the date to ensure it's valid
    const date = new Date(dateValue);
    return !isNaN(date.getTime());
  }

  /**
   * Validate score (should be numeric and within reasonable range)
   */
  private validateScore(scoreValue: any): boolean {
    const score = parseFloat(scoreValue);
    if (isNaN(score)) {
      return false;
    }

    // Reasonable score range (0-120 to accommodate TOEFL scores)
    return score >= 0 && score <= 120;
  }

  /**
   * Validate email format
   */
  private validateEmail(emailValue: any): boolean {
    if (typeof emailValue !== 'string') {
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(emailValue);
  }

  /**
   * Validate URL format
   */
  private validateUrl(urlValue: any): boolean {
    if (typeof urlValue !== 'string') {
      return false;
    }

    try {
      new URL(urlValue);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate alphanumeric values
   */
  private validateAlphanumeric(value: any): boolean {
    if (typeof value !== 'string') {
      return false;
    }

    return /^[a-zA-Z0-9]+$/.test(value);
  }

  /**
   * Get validation error message for a field
   */
  private getValidationError(fieldName: string, fieldValue: any): string {
    const fieldNameLower = fieldName.toLowerCase();

    if (fieldNameLower.includes('date')) {
      return 'Invalid date format';
    }

    if (fieldNameLower.includes('score')) {
      return 'Invalid score value';
    }

    if (fieldNameLower.includes('email')) {
      return 'Invalid email format';
    }

    if (fieldNameLower.includes('link') || fieldNameLower.includes('url')) {
      return 'Invalid URL format';
    }

    return 'Invalid field value';
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Create validation context for document processing
   */
  createValidationContext(
    documentType: string,
    options: {
      testType?: string;
      requiredFieldsFromFrontend?: string[];
      additionalValidationRules?: Record<string, (value: any) => boolean>;
    } = {},
  ): ValidationContext {
    return {
      documentType,
      testType: options.testType,
      requiredFieldsFromFrontend: options.requiredFieldsFromFrontend,
      additionalValidationRules: options.additionalValidationRules,
    };
  }
}
