import { Injectable } from '@nestjs/common';
import { DynamoDBService } from '../../common/dynamodb.service';

export interface PicklistMappingResult {
  label: string;
  value: string;
  labelField: string;
  valueField: string;
}

export interface PicklistMappingError {
  field: string;
  originalValue: string;
  error: string;
}

@Injectable()
export class OcrPicklistMappingService {
  constructor(private readonly dynamoDBService: DynamoDBService) {}

  /**
   * Map OCR field values to picklist values
   */
  async mapOcrFieldsToPicklist(
    ocrData: any,
    picklistMappings: Record<string, string>,
    documentType: string,
  ): Promise<{
    mappedData: Record<string, any>;
    errors: PicklistMappingError[];
  }> {
    const mappedData = { ...ocrData };
    const errors: PicklistMappingError[] = [];

    for (const [ocrField, picklistKey] of Object.entries(picklistMappings)) {
      const ocrValue = this.getNestedValue(ocrData, ocrField);
      
      if (ocrValue) {
        try {
          const mappingResult = await this.mapFieldToPicklist(
            ocrField,
            ocrValue,
            picklistKey,
          );

          if (mappingResult) {
            // Set both label and value fields
            mappedData[mappingResult.labelField] = mappingResult.label;
            mappedData[mappingResult.valueField] = mappingResult.value;
          } else {
            errors.push({
              field: ocrField,
              originalValue: ocrValue,
              error: `No picklist mapping found for value: ${ocrValue}`,
            });
          }
        } catch (error) {
          errors.push({
            field: ocrField,
            originalValue: ocrValue,
            error: error.message,
          });
        }
      }
    }

    return { mappedData, errors };
  }

  /**
   * Map a single field value to picklist
   */
  private async mapFieldToPicklist(
    fieldName: string,
    fieldValue: string,
    picklistKey: string,
  ): Promise<PicklistMappingResult | null> {
    try {
      const picklistData = await this.getPicklistData(picklistKey);
      
      if (!picklistData || !picklistData.data) {
        return null;
      }

      // Try exact match first
      let matchedItem = picklistData.data.find(
        (item: any) => 
          item.label?.toLowerCase() === fieldValue.toLowerCase() ||
          item.value?.toLowerCase() === fieldValue.toLowerCase()
      );

      // If no exact match, try partial match
      if (!matchedItem) {
        matchedItem = picklistData.data.find(
          (item: any) =>
            item.label?.toLowerCase().includes(fieldValue.toLowerCase()) ||
            fieldValue.toLowerCase().includes(item.label?.toLowerCase()) ||
            item.value?.toLowerCase().includes(fieldValue.toLowerCase()) ||
            fieldValue.toLowerCase().includes(item.value?.toLowerCase())
        );
      }

      if (matchedItem) {
        return {
          label: matchedItem.label,
          value: matchedItem.value,
          labelField: this.getLabelFieldName(fieldName),
          valueField: this.getValueFieldName(fieldName),
        };
      }

      return null;
    } catch (error) {
      console.error(`Error mapping field ${fieldName} to picklist:`, error);
      throw error;
    }
  }

  /**
   * Get picklist data from DynamoDB
   */
  private async getPicklistData(picklistKey: string): Promise<any> {
    try {
      const result = await this.dynamoDBService.getObject(
        `gus-eip-picklist-${process.env.STAGE}`,
        {
          PK: 'en',
          SK: picklistKey,
        },
      );

      return result?.Item || null;
    } catch (error) {
      console.error(`Error fetching picklist data for key ${picklistKey}:`, error);
      throw error;
    }
  }

  /**
   * Map country name to country code (specific for passport documents)
   */
  async mapCountryToCode(countryName: string): Promise<{
    countryCode: string;
    countryDisplayName: string;
  } | null> {
    try {
      const countryMap = await this.dynamoDBService.getObject(
        `gus-eip-picklist-${process.env.STAGE}`,
        {
          PK: 'en',
          SK: 'countryMap',
        },
      );

      const countryCode = countryMap?.Item?.data?.[countryName] || '';
      
      return {
        countryCode,
        countryDisplayName: countryName,
      };
    } catch (error) {
      console.error('Error mapping country to code:', error);
      return null;
    }
  }

  /**
   * Map English test name to standardized format
   */
  async mapEnglishTestName(testName: string): Promise<{
    testNameLabel: string;
    testNameValue: string;
  } | null> {
    try {
      const testMapping = await this.mapFieldToPicklist(
        'name_of_test',
        testName,
        'englishTestTypes',
      );

      if (testMapping) {
        return {
          testNameLabel: testMapping.label,
          testNameValue: testMapping.value,
        };
      }

      // If no mapping found, return the original value
      return {
        testNameLabel: testName,
        testNameValue: testName.toLowerCase().replace(/\s+/g, '_'),
      };
    } catch (error) {
      console.error('Error mapping English test name:', error);
      return {
        testNameLabel: testName,
        testNameValue: testName.toLowerCase().replace(/\s+/g, '_'),
      };
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Generate label field name
   */
  private getLabelFieldName(originalFieldName: string): string {
    const baseFieldName = originalFieldName.replace(/\./g, '_');
    return `${baseFieldName}_label`;
  }

  /**
   * Generate value field name
   */
  private getValueFieldName(originalFieldName: string): string {
    const baseFieldName = originalFieldName.replace(/\./g, '_');
    return `${baseFieldName}_value`;
  }

  /**
   * Validate and clean OCR data before processing
   */
  validateAndCleanOcrData(ocrData: any): any {
    const cleanedData = { ...ocrData };

    // Clean up common OCR artifacts
    Object.keys(cleanedData).forEach(key => {
      if (typeof cleanedData[key] === 'string') {
        cleanedData[key] = cleanedData[key]
          .trim()
          .replace(/\s+/g, ' ') // Replace multiple spaces with single space
          .replace(/[^\w\s\-\.\/]/g, ''); // Remove special characters except common ones
      }
    });

    return cleanedData;
  }
}
