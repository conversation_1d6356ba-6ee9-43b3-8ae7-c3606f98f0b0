/**
 * OCR Document Configuration
 * Defines document types, their OCR types, required fields, and field mappings
 */

export interface DocumentTypeConfig {
  ocrDocumentType: string;
  requiredFields: string[];
  fieldMappings: Record<string, string>;
  picklistMappings?: Record<string, string>;
  testSpecificFields?: Record<string, string[]>;
}

export interface OcrFieldValidationResult {
  isValid: boolean;
  missingFields: string[];
}

export interface PicklistMapping {
  field: string;
  labelField: string;
  valueField: string;
}

/**
 * Document type configurations
 */
export const DOCUMENT_TYPE_CONFIGS: Record<string, DocumentTypeConfig> = {
  // Passport/ID document configuration
  'id/passport': {
    ocrDocumentType: 'Passport',
    requiredFields: [
      'first_name',
      'last_name',
      'date_of_birth',
      'document_number',
      'issue_date',
      'issuing_country',
      'expiry_date',
      'issuing_country_code',
    ],
    fieldMappings: {
      first_name: 'passportFirstName',
      last_name: 'passportLastName',
      date_of_birth: 'birthDate',
      document_number: 'passportNumber',
      expiry_date: 'passportExpiryDate',
      issue_date: 'passportIssueDate',
      issuing_country: 'passportIssuingCountryDisplayName',
      issuing_country_code: 'passportIssuingCountry',
      place_of_birth: 'placeOfBirth',
    },
    picklistMappings: {
      issuing_country: 'countryMap',
    },
  },

  // Proof of English Proficiency configuration
  'proof english proficiency': {
    ocrDocumentType: 'Proof of English',
    requiredFields: [], // Will be determined dynamically based on test type
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
      'test_scores.listening': 'listeningScore',
      'test_scores.reading': 'readingScore',
      'test_scores.writing': 'writingScore',
      'test_scores.speaking': 'speakingScore',
      trf_number: 'trfNumber',
      test_report_link: 'testReportLink',
    },
    picklistMappings: {
      name_of_test: 'englishTestTypes',
    },
    // Test-specific required fields
    testSpecificFields: {
      duolingo: [
        'name_of_test',
        'test_date',
        'test_scores.overall_score',
      ],
      ielts: [
        'name_of_test',
        'test_date',
        'test_scores.overall_score',
        'test_scores.listening',
        'test_scores.reading',
        'test_scores.writing',
        'test_scores.speaking',
        'trf_number',
        'test_report_link',
      ],
      toefl: [
        'name_of_test',
        'test_date',
        'test_scores.overall_score',
        'test_scores.listening',
        'test_scores.reading',
        'test_scores.writing',
        'test_scores.speaking',
      ],
      pte: [
        'name_of_test',
        'test_date',
        'test_scores.overall_score',
        'test_scores.listening',
        'test_scores.reading',
        'test_scores.writing',
        'test_scores.speaking',
      ],
    },
  },
};

/**
 * Get document type configuration
 */
export function getDocumentTypeConfig(documentType: string): DocumentTypeConfig | null {
  return DOCUMENT_TYPE_CONFIGS[documentType.toLowerCase()] || null;
}

/**
 * Get OCR document type for a given document type
 */
export function getOcrDocumentType(documentType: string): string {
  const config = getDocumentTypeConfig(documentType);
  return config?.ocrDocumentType || 'Unknown';
}

/**
 * Get required fields for a document type
 * For English proficiency tests, this will be determined by the test type
 */
export function getRequiredFields(
  documentType: string,
  testType?: string,
  requiredFieldsFromFrontend?: string[]
): string[] {
  const config = getDocumentTypeConfig(documentType);
  if (!config) return [];

  // For proof of English proficiency, use test-specific fields or frontend-provided fields
  if (documentType.toLowerCase() === 'proof english proficiency') {
    if (requiredFieldsFromFrontend && requiredFieldsFromFrontend.length > 0) {
      return requiredFieldsFromFrontend;
    }
    
    if (testType && config.testSpecificFields) {
      return config.testSpecificFields[testType.toLowerCase()] || [];
    }
    
    return [];
  }

  return config.requiredFields;
}

/**
 * Get field mappings for a document type
 */
export function getFieldMappings(documentType: string): Record<string, string> {
  const config = getDocumentTypeConfig(documentType);
  return config?.fieldMappings || {};
}

/**
 * Get picklist mappings for a document type
 */
export function getPicklistMappings(documentType: string): Record<string, string> {
  const config = getDocumentTypeConfig(documentType);
  return config?.picklistMappings || {};
}

/**
 * Check if a document type supports OCR processing
 */
export function isOcrSupportedDocumentType(documentType: string): boolean {
  return getDocumentTypeConfig(documentType) !== null;
}

/**
 * Get all supported document types
 */
export function getSupportedDocumentTypes(): string[] {
  return Object.keys(DOCUMENT_TYPE_CONFIGS);
}
