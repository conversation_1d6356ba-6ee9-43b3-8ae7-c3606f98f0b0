/**
 * English Proficiency Test Mappings
 * Defines test-specific field mappings and validation rules
 */

export interface EnglishTestMapping {
  testName: string;
  requiredFields: string[];
  fieldMappings: Record<string, string>;
  scoreFields?: string[];
  additionalFields?: string[];
}

/**
 * English proficiency test configurations
 */
export const ENGLISH_TEST_MAPPINGS: Record<string, EnglishTestMapping> = {
  duolingo: {
    testName: 'Duolingo English Test',
    requiredFields: [
      'name_of_test',
      'test_date',
      'test_scores.overall_score',
    ],
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
    },
    scoreFields: ['test_scores.overall_score'],
  },

  ielts: {
    testName: 'IELTS',
    requiredFields: [
      'name_of_test',
      'test_date',
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
      'trf_number',
      'test_report_link',
    ],
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
      'test_scores.listening': 'listeningScore',
      'test_scores.reading': 'readingScore',
      'test_scores.writing': 'writingScore',
      'test_scores.speaking': 'speakingScore',
      trf_number: 'trfNumber',
      test_report_link: 'testReportLink',
    },
    scoreFields: [
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
    additionalFields: ['trf_number', 'test_report_link'],
  },

  toefl: {
    testName: 'TOEFL',
    requiredFields: [
      'name_of_test',
      'test_date',
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
      'test_scores.listening': 'listeningScore',
      'test_scores.reading': 'readingScore',
      'test_scores.writing': 'writingScore',
      'test_scores.speaking': 'speakingScore',
    },
    scoreFields: [
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
  },

  pte: {
    testName: 'PTE Academic',
    requiredFields: [
      'name_of_test',
      'test_date',
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
      'test_scores.listening': 'listeningScore',
      'test_scores.reading': 'readingScore',
      'test_scores.writing': 'writingScore',
      'test_scores.speaking': 'speakingScore',
    },
    scoreFields: [
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
  },

  cambridge: {
    testName: 'Cambridge English',
    requiredFields: [
      'name_of_test',
      'test_date',
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
    fieldMappings: {
      name_of_test: 'testName',
      test_date: 'testDate',
      'test_scores.overall_score': 'overallScore',
      'test_scores.listening': 'listeningScore',
      'test_scores.reading': 'readingScore',
      'test_scores.writing': 'writingScore',
      'test_scores.speaking': 'speakingScore',
    },
    scoreFields: [
      'test_scores.overall_score',
      'test_scores.listening',
      'test_scores.reading',
      'test_scores.writing',
      'test_scores.speaking',
    ],
  },
};

/**
 * Get English test mapping by test name
 */
export function getEnglishTestMapping(testName: string): EnglishTestMapping | null {
  const normalizedTestName = testName.toLowerCase().trim();
  
  // Try exact match first
  if (ENGLISH_TEST_MAPPINGS[normalizedTestName]) {
    return ENGLISH_TEST_MAPPINGS[normalizedTestName];
  }

  // Try partial matches for common variations
  for (const [key, mapping] of Object.entries(ENGLISH_TEST_MAPPINGS)) {
    if (normalizedTestName.includes(key) || key.includes(normalizedTestName)) {
      return mapping;
    }
  }

  return null;
}

/**
 * Get required fields for a specific English test
 */
export function getEnglishTestRequiredFields(testName: string): string[] {
  const mapping = getEnglishTestMapping(testName);
  return mapping?.requiredFields || [];
}

/**
 * Get field mappings for a specific English test
 */
export function getEnglishTestFieldMappings(testName: string): Record<string, string> {
  const mapping = getEnglishTestMapping(testName);
  return mapping?.fieldMappings || {};
}

/**
 * Get all supported English test types
 */
export function getSupportedEnglishTests(): string[] {
  return Object.keys(ENGLISH_TEST_MAPPINGS);
}

/**
 * Detect test type from OCR response
 */
export function detectTestTypeFromOcr(ocrData: any): string | null {
  const testName = ocrData.name_of_test?.toLowerCase()?.trim();
  if (!testName) return null;

  // Try to match against known test types
  for (const [key, mapping] of Object.entries(ENGLISH_TEST_MAPPINGS)) {
    if (testName.includes(key) || testName.includes(mapping.testName.toLowerCase())) {
      return key;
    }
  }

  return null;
}
