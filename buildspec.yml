version: 0.2
run-as: root

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - node --version
      - echo "Setting SHELL environment variable..."
      - export SHELL=/bin/bash
      - npm install -g pnpm@8
      - echo "Running pnpm install..."
      - pnpm install
      - echo "Setting up pnpm..."
      - pnpm setup
      - echo "Configuring pnpm to use a custom global bin directory..."
      - mkdir -p /root/.pnpm-global
      - pnpm config set global-dir /root/.pnpm-global
      - echo 'export PATH=$PATH:/root/.pnpm-global/bin' >> /root/.bashrc
      - source /root/.bashrc
      - echo "Installing Serverless Framework..."
      - pnpm install -g serverless@3.38.0

      # Install SonarScanner
      # - echo Installing SonarScanner...
      # - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-5.0.1.3006-linux.zip
      # - unzip sonar-scanner-cli-*.zip
      # - mv sonar-scanner-*/ sonar-scanner
      # - export PATH=$PATH:$(pwd)/sonar-scanner/bin
  build:
    commands:
      # SonarQube scan
      # - echo Running SonarScanner...
      # - sonar-scanner -X -Dsonar.projectKey=oap-backend -Dsonar.sources=. -Dsonar.host.url=http://**************:9000 -Dsonar.login=squ_07f9c1882056ad1d276911123074bf62b40b9242

      - echo "Deploying with Serverless Framework..."
      - sls deploy --stage ${stage} --verbose
cache:
  paths:
    - node_modules
